/**
 * Centralized logging utility with environment-based controls
 * Reduces console noise in production while maintaining debug capabilities
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LogConfig {
  enabled: boolean;
  level: LogLevel;
  modules: {
    auth: boolean;
    navigation: boolean;
    api: boolean;
    demo: boolean;
    general: boolean;
  };
}

// Default configuration - can be overridden via localStorage
const defaultConfig: LogConfig = {
  enabled: process.env.NODE_ENV === 'development',
  level: 'info',
  modules: {
    auth: false,        // Disable auth logs by default (too verbose)
    navigation: false,  // Disable navigation logs by default
    api: true,         // Keep API logs (important for debugging)
    demo: false,       // Disable demo data logs by default
    general: true      // Keep general logs
  }
};

class Logger {
  private config: LogConfig;

  constructor() {
    this.config = this.loadConfig();
  }

  private loadConfig(): LogConfig {
    try {
      const savedConfig = localStorage.getItem('app-log-config');
      if (savedConfig) {
        return { ...defaultConfig, ...JSON.parse(savedConfig) };
      }
    } catch (error) {
      // Ignore localStorage errors
    }
    return defaultConfig;
  }

  private saveConfig(): void {
    try {
      localStorage.setItem('app-log-config', JSON.stringify(this.config));
    } catch (error) {
      // Ignore localStorage errors
    }
  }

  private shouldLog(module: keyof LogConfig['modules'], level: LogLevel): boolean {
    if (!this.config.enabled) return false;
    if (!this.config.modules[module]) return false;
    
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(this.config.level);
    const messageLevelIndex = levels.indexOf(level);
    
    return messageLevelIndex >= currentLevelIndex;
  }

  private formatMessage(module: string, message: string): string {
    return `[${module.toUpperCase()}] ${message}`;
  }

  // Auth module logging
  auth = {
    debug: (message: string, ...args: any[]) => {
      if (this.shouldLog('auth', 'debug')) {
        console.debug(this.formatMessage('auth', message), ...args);
      }
    },
    info: (message: string, ...args: any[]) => {
      if (this.shouldLog('auth', 'info')) {
        console.log(this.formatMessage('auth', message), ...args);
      }
    },
    warn: (message: string, ...args: any[]) => {
      if (this.shouldLog('auth', 'warn')) {
        console.warn(this.formatMessage('auth', message), ...args);
      }
    },
    error: (message: string, ...args: any[]) => {
      if (this.shouldLog('auth', 'error')) {
        console.error(this.formatMessage('auth', message), ...args);
      }
    }
  };

  // Navigation module logging
  navigation = {
    debug: (message: string, ...args: any[]) => {
      if (this.shouldLog('navigation', 'debug')) {
        console.debug(this.formatMessage('navigation', message), ...args);
      }
    },
    info: (message: string, ...args: any[]) => {
      if (this.shouldLog('navigation', 'info')) {
        console.log(this.formatMessage('navigation', message), ...args);
      }
    },
    warn: (message: string, ...args: any[]) => {
      if (this.shouldLog('navigation', 'warn')) {
        console.warn(this.formatMessage('navigation', message), ...args);
      }
    },
    error: (message: string, ...args: any[]) => {
      if (this.shouldLog('navigation', 'error')) {
        console.error(this.formatMessage('navigation', message), ...args);
      }
    }
  };

  // API module logging
  api = {
    debug: (message: string, ...args: any[]) => {
      if (this.shouldLog('api', 'debug')) {
        console.debug(this.formatMessage('api', message), ...args);
      }
    },
    info: (message: string, ...args: any[]) => {
      if (this.shouldLog('api', 'info')) {
        console.log(this.formatMessage('api', message), ...args);
      }
    },
    warn: (message: string, ...args: any[]) => {
      if (this.shouldLog('api', 'warn')) {
        console.warn(this.formatMessage('api', message), ...args);
      }
    },
    error: (message: string, ...args: any[]) => {
      if (this.shouldLog('api', 'error')) {
        console.error(this.formatMessage('api', message), ...args);
      }
    }
  };

  // Demo module logging
  demo = {
    debug: (message: string, ...args: any[]) => {
      if (this.shouldLog('demo', 'debug')) {
        console.debug(this.formatMessage('demo', message), ...args);
      }
    },
    info: (message: string, ...args: any[]) => {
      if (this.shouldLog('demo', 'info')) {
        console.log(this.formatMessage('demo', message), ...args);
      }
    },
    warn: (message: string, ...args: any[]) => {
      if (this.shouldLog('demo', 'warn')) {
        console.warn(this.formatMessage('demo', message), ...args);
      }
    },
    error: (message: string, ...args: any[]) => {
      if (this.shouldLog('demo', 'error')) {
        console.error(this.formatMessage('demo', message), ...args);
      }
    }
  };

  // General logging
  debug = (message: string, ...args: any[]) => {
    if (this.shouldLog('general', 'debug')) {
      console.debug(this.formatMessage('general', message), ...args);
    }
  };

  info = (message: string, ...args: any[]) => {
    if (this.shouldLog('general', 'info')) {
      console.log(this.formatMessage('general', message), ...args);
    }
  };

  warn = (message: string, ...args: any[]) => {
    if (this.shouldLog('general', 'warn')) {
      console.warn(this.formatMessage('general', message), ...args);
    }
  };

  error = (message: string, ...args: any[]) => {
    if (this.shouldLog('general', 'error')) {
      console.error(this.formatMessage('general', message), ...args);
    }
  };

  // Configuration methods
  enableModule = (module: keyof LogConfig['modules']) => {
    this.config.modules[module] = true;
    this.saveConfig();
  };

  disableModule = (module: keyof LogConfig['modules']) => {
    this.config.modules[module] = false;
    this.saveConfig();
  };

  setLevel = (level: LogLevel) => {
    this.config.level = level;
    this.saveConfig();
  };

  enable = () => {
    this.config.enabled = true;
    this.saveConfig();
  };

  disable = () => {
    this.config.enabled = false;
    this.saveConfig();
  };

  getConfig = () => ({ ...this.config });

  // Quick enable/disable for debugging
  enableAll = () => {
    this.config.enabled = true;
    Object.keys(this.config.modules).forEach(module => {
      this.config.modules[module as keyof LogConfig['modules']] = true;
    });
    this.saveConfig();
    console.log('🔧 All logging modules enabled');
  };

  disableAll = () => {
    Object.keys(this.config.modules).forEach(module => {
      this.config.modules[module as keyof LogConfig['modules']] = false;
    });
    this.saveConfig();
    console.log('🔇 All logging modules disabled');
  };
}

// Create singleton instance
const logger = new Logger();

// Export for global access
export default logger;

// Also export for window access (debugging)
if (typeof window !== 'undefined') {
  (window as any).logger = logger;
}

// Helper function to show current config
export const showLogConfig = () => {
  console.table(logger.getConfig());
};
