# Port Configuration Guide

## 🚀 Development Ports

### Frontend (Next.js)
- **Port**: `5173`
- **URL**: `http://localhost:5173`
- **Command**: `npm run dev`

### Backend/Server Side (Future Implementation)
- **Port**: `5000`
- **URL**: `http://localhost:5000`
- **Command**: TBD (when backend is implemented)

## 📝 Available Scripts

### Frontend Only
```bash
# Start Next.js development server on port 5173
npm run dev

# Start production server on port 5173
npm run start
```

### Full Development Environment
```bash
# Start both frontend and backend (when available)
npm run dev:server
```

## ⚙️ Configuration Files

### Environment Variables (.env.local)
```env
# Frontend port
PORT=5173

# Backend port (for future use)
SERVER_PORT=5000

# API Configuration
NEXT_PUBLIC_API_BASE_URL=https://api.chhrone.web.id
```

### Package.json <PERSON>
- `dev`: Start Next.js on port 5173
- `dev:server`: Start full development environment
- `start`: Start production server on port 5173

## 🔧 Port Customization

### Change Frontend Port
1. Update `.env.local`:
   ```env
   PORT=YOUR_DESIRED_PORT
   ```

2. Or use command line:
   ```bash
   npm run dev -- -p YOUR_DESIRED_PORT
   ```

### Change Backend Port (Future)
1. Update `.env.local`:
   ```env
   SERVER_PORT=YOUR_DESIRED_PORT
   ```

## 🌐 API Proxy Routes

The application uses Next.js API routes as proxy to avoid CORS:

- **Login**: `/api/proxy/auth/login` → `https://api.chhrone.web.id/api/auth/login`
- **Register**: `/api/proxy/auth/register` → `https://api.chhrone.web.id/api/auth/register`
- **Health**: `/api/proxy/health` → `https://api.chhrone.web.id/health`

## 🚨 Troubleshooting

### Port Already in Use
```bash
# Kill process using port 5173
npx kill-port 5173

# Or find and kill manually
netstat -ano | findstr :5173
taskkill /PID <PID_NUMBER> /F
```

### Connection Refused Error
1. Make sure development server is running
2. Check if port is correct in browser URL
3. Verify `.env.local` configuration
4. Check firewall settings

## 📋 Quick Start

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Start development server**:
   ```bash
   npm run dev
   ```

3. **Open browser**:
   ```
   http://localhost:5173
   ```

## 🔗 Related Files

- `package.json` - Script definitions
- `.env.local` - Environment variables
- `scripts/dev-server.js` - Development server launcher
- `next.config.mjs` - Next.js configuration
