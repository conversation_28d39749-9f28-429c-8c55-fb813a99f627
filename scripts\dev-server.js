#!/usr/bin/env node

/**
 * Development Server Launcher
 * Starts Next.js on port 5173 and optionally a backend server on port 5000
 */

const { spawn } = require('child_process');
const path = require('path');

// Configuration
const FRONTEND_PORT = 5173;
const BACKEND_PORT = 5000;

console.log('🚀 Starting Development Servers...\n');

// Start Next.js Frontend Server
console.log(`📱 Starting Frontend (Next.js) on port ${FRONTEND_PORT}...`);
const frontend = spawn('npm', ['run', 'dev'], {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd(),
  env: {
    ...process.env,
    PORT: FRONTEND_PORT.toString()
  }
});

frontend.on('error', (error) => {
  console.error('❌ Frontend server error:', error);
});

frontend.on('close', (code) => {
  console.log(`📱 Frontend server exited with code ${code}`);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down servers...');
  frontend.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down servers...');
  frontend.kill('SIGTERM');
  process.exit(0);
});

console.log(`\n✅ Servers starting...`);
console.log(`📱 Frontend: http://localhost:${FRONTEND_PORT}`);
console.log(`🔧 Backend: http://localhost:${BACKEND_PORT} (if implemented)`);
console.log('\n💡 Press Ctrl+C to stop all servers\n');
