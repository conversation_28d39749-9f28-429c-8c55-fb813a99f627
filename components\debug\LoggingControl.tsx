'use client';

import React, { useState, useEffect } from 'react';
import logger, { showLogConfig } from '../../utils/logger';

interface LoggingControlProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function LoggingControl({ isOpen, onClose }: LoggingControlProps) {
  const [config, setConfig] = useState(logger.getConfig());

  useEffect(() => {
    if (isOpen) {
      setConfig(logger.getConfig());
    }
  }, [isOpen]);

  const handleModuleToggle = (module: keyof typeof config.modules) => {
    if (config.modules[module]) {
      logger.disableModule(module);
    } else {
      logger.enableModule(module);
    }
    setConfig(logger.getConfig());
  };

  const handleLevelChange = (level: 'debug' | 'info' | 'warn' | 'error') => {
    logger.setLevel(level);
    setConfig(logger.getConfig());
  };

  const handleGlobalToggle = () => {
    if (config.enabled) {
      logger.disable();
    } else {
      logger.enable();
    }
    setConfig(logger.getConfig());
  };

  const handleEnableAll = () => {
    logger.enableAll();
    setConfig(logger.getConfig());
  };

  const handleDisableAll = () => {
    logger.disableAll();
    setConfig(logger.getConfig());
  };

  const handleShowConfig = () => {
    showLogConfig();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[80vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-900">Logging Control</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Global Controls */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Global Settings</h3>
          
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium">Logging Enabled</span>
            <button
              onClick={handleGlobalToggle}
              className={`px-3 py-1 rounded text-sm font-medium ${
                config.enabled
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}
            >
              {config.enabled ? 'ON' : 'OFF'}
            </button>
          </div>

          <div className="mb-3">
            <label className="block text-sm font-medium mb-1">Log Level</label>
            <select
              value={config.level}
              onChange={(e) => handleLevelChange(e.target.value as any)}
              className="w-full px-3 py-1 border border-gray-300 rounded text-sm"
              disabled={!config.enabled}
            >
              <option value="debug">Debug</option>
              <option value="info">Info</option>
              <option value="warn">Warning</option>
              <option value="error">Error</option>
            </select>
          </div>

          <div className="flex gap-2">
            <button
              onClick={handleEnableAll}
              className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
              disabled={!config.enabled}
            >
              Enable All
            </button>
            <button
              onClick={handleDisableAll}
              className="px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600"
            >
              Disable All
            </button>
          </div>
        </div>

        {/* Module Controls */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Module Settings</h3>
          
          {Object.entries(config.modules).map(([module, enabled]) => (
            <div key={module} className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium capitalize">{module}</span>
              <button
                onClick={() => handleModuleToggle(module as keyof typeof config.modules)}
                className={`px-3 py-1 rounded text-sm font-medium ${
                  enabled && config.enabled
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-600'
                }`}
                disabled={!config.enabled}
              >
                {enabled ? 'ON' : 'OFF'}
              </button>
            </div>
          ))}
        </div>

        {/* Info */}
        <div className="mb-4 p-3 bg-blue-50 rounded">
          <h4 className="text-sm font-semibold text-blue-800 mb-1">Quick Tips:</h4>
          <ul className="text-xs text-blue-700 space-y-1">
            <li>• Auth logs are disabled by default (too verbose)</li>
            <li>• API logs show important connectivity info</li>
            <li>• Demo logs show data initialization</li>
            <li>• Settings are saved in localStorage</li>
          </ul>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <button
            onClick={handleShowConfig}
            className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
          >
            Show Config in Console
          </button>
          <button
            onClick={onClose}
            className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
          >
            Close
          </button>
        </div>

        {/* Current Status */}
        <div className="mt-4 p-2 bg-gray-50 rounded text-xs">
          <strong>Current Status:</strong> {config.enabled ? 'Enabled' : 'Disabled'} | 
          Level: {config.level} | 
          Active Modules: {Object.entries(config.modules).filter(([_, enabled]) => enabled).length}
        </div>
      </div>
    </div>
  );
}
